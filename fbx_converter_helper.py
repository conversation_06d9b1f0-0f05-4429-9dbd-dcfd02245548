#!/usr/bin/env python3
# FBX to OBJ Conversion Helper
# This script shows how to convert FBX files using Blender in batch

import subprocess
import os

def convert_fbx_to_obj_with_blender(fbx_file, output_dir):
    """Convert FBX to OBJ using Blender command line"""
    obj_file = os.path.join(output_dir, os.path.splitext(os.path.basename(fbx_file))[0] + '.obj')
    
    # Blender command to convert FBX to OBJ
    cmd = [
        'blender',  # Make sure Blender is in PATH
        '--background',
        '--python-expr',
        f'''
import bpy
bpy.ops.wm.read_factory_settings(use_empty=True)
bpy.ops.import_scene.fbx(filepath="{fbx_file}")
bpy.ops.export_scene.obj(filepath="{obj_file}")
'''
    ]
    
    try:
        subprocess.run(cmd, check=True)
        print(f"Converted: {fbx_file} -> {obj_file}")
        return True
    except subprocess.CalledProcessError:
        print(f"Failed to convert: {fbx_file}")
        return False

# Example usage:
# convert_fbx_to_obj_with_blender("model.fbx", "./converted/")
