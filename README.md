# 3D Model Image Generator

This project provides Python scripts to automatically generate front, side, and back view images from 3D model files. It supports various 3D file formats and creates organized output folders for each model.

## Features

- **Multiple 3D Format Support**: OBJ, FBX, PLY, STL, DAE, GLTF, GLB
- **Automatic View Generation**: Front, side, and back views
- **Organized Output**: Creates separate folders for each 3D model
- **Two Rendering Options**: Advanced (pyrender) and Simple (matplotlib)
- **Batch Processing**: Processes all 3D files in a directory
- **Configurable**: Adjustable image size, DPI, and output settings

## Files Overview

1. **`3d_image_generator.py`** - Advanced renderer using pyrender (supports all formats)
2. **`simple_3d_renderer.py`** - Simple renderer using matplotlib (OBJ/PLY only)
3. **`setup.py`** - Installation script for dependencies
4. **`requirements.txt`** - List of required Python packages

## Installation

### Option 1: Automatic Setup
```bash
python setup.py
```

### Option 2: Manual Installation
```bash
# For basic functionality (OBJ/PLY files)
pip install numpy matplotlib Pillow

# For full functionality (all formats)
pip install -r requirements.txt
```

## Usage

### Basic Usage (Current Directory)
```bash
# Using the simple renderer (OBJ/PLY files only)
python simple_3d_renderer.py

# Using the advanced renderer (all formats)
python 3d_image_generator.py
```

### Advanced Usage
```bash
# Specify input and output directories
python 3d_image_generator.py --input-dir /path/to/3d/files --output-dir /path/to/output

# Search recursively in subdirectories
python 3d_image_generator.py --recursive

# Custom image size (width x height)
python 3d_image_generator.py --image-size 1024 768

# For simple renderer with custom DPI
python simple_3d_renderer.py --dpi 150 --image-size 12 9
```

## Command Line Options

### 3d_image_generator.py
- `--input-dir, -i`: Input directory containing 3D files (default: current directory)
- `--output-dir, -o`: Output directory for generated images (default: ./rendered_views)
- `--image-size, -s`: Output image size in pixels (width height, default: 800 600)
- `--recursive, -r`: Search recursively in subdirectories

### simple_3d_renderer.py
- `--input-dir, -i`: Input directory containing 3D files (default: current directory)
- `--output-dir, -o`: Output directory for generated images (default: ./rendered_views)
- `--image-size, -s`: Output image size in inches (width height, default: 10 8)
- `--dpi, -d`: DPI for output images (default: 100)
- `--recursive, -r`: Search recursively in subdirectories

## Output Structure

The scripts create the following folder structure:
```
rendered_views/
├── model1/
│   ├── front.png
│   ├── side.png
│   └── back.png
├── model2/
│   ├── front.png
│   ├── side.png
│   └── back.png
└── ...
```

## Supported File Formats

### Advanced Renderer (3d_image_generator.py)
- ✅ OBJ (.obj)
- ✅ FBX (.fbx)
- ✅ PLY (.ply)
- ✅ STL (.stl)
- ✅ DAE (.dae)
- ✅ GLTF (.gltf)
- ✅ GLB (.glb)

### Simple Renderer (simple_3d_renderer.py)
- ✅ OBJ (.obj)
- ✅ PLY (.ply)

## Troubleshooting

### Common Issues

1. **"No module named 'pyrender'"**
   - Run: `python setup.py` or install manually: `pip install pyrender`

2. **OpenGL errors with pyrender**
   - Use the simple renderer: `python simple_3d_renderer.py`
   - Or install OpenGL drivers for your system

3. **FBX files not loading**
   - Ensure you have the latest version of trimesh: `pip install --upgrade trimesh`
   - Some FBX files may require additional dependencies

4. **Memory issues with large models**
   - The simple renderer limits faces to 1000 for performance
   - Consider reducing image size or DPI

### Performance Tips

- Use the simple renderer for basic visualization needs
- Reduce image size for faster processing: `--image-size 400 300`
- Use lower DPI for the simple renderer: `--dpi 72`

## Examples

### Process all 3D files in current directory
```bash
python 3d_image_generator.py
```

### Process specific directory with custom output
```bash
python 3d_image_generator.py --input-dir ./models --output-dir ./images
```

### High-quality output
```bash
python 3d_image_generator.py --image-size 1920 1080
```

### Quick preview with simple renderer
```bash
python simple_3d_renderer.py --dpi 72 --image-size 6 4
```

## Requirements

- Python 3.7+
- NumPy
- Matplotlib (for simple renderer)
- Pillow
- Trimesh (for advanced renderer)
- Pyrender (for advanced renderer)
- PyOpenGL (for advanced renderer)

## License

This project is provided as-is for educational and research purposes.
