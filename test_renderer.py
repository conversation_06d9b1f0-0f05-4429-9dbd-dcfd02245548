#!/usr/bin/env python3
"""
Test script for the 3D renderers

This script tests both renderers with a sample of your 3D files.
"""

import os
import sys
import subprocess

def check_dependencies():
    """Check if required dependencies are installed"""
    try:
        import numpy
        import matplotlib
        from PIL import Image
        print("✓ Basic dependencies available")
        basic_ok = True
    except ImportError as e:
        print(f"✗ Basic dependency missing: {e}")
        basic_ok = False
    
    try:
        import trimesh
        import pyrender
        print("✓ Advanced dependencies available")
        advanced_ok = True
    except ImportError as e:
        print(f"⚠ Advanced dependency missing: {e}")
        advanced_ok = False
    
    return basic_ok, advanced_ok

def find_sample_files():
    """Find a few sample 3D files for testing"""
    extensions = ['.obj', '.fbx', '.ply']
    files = []
    
    for ext in extensions:
        for file in os.listdir('.'):
            if file.lower().endswith(ext):
                files.append(file)
                if len(files) >= 3:  # Limit to 3 files for testing
                    break
        if len(files) >= 3:
            break
    
    return files

def test_simple_renderer(test_files):
    """Test the simple renderer"""
    print("\nTesting simple renderer...")
    
    # Filter to only OBJ files for simple renderer
    obj_files = [f for f in test_files if f.lower().endswith('.obj')]
    
    if not obj_files:
        print("No OBJ files found for simple renderer test")
        return False
    
    try:
        cmd = [
            sys.executable, 'simple_3d_renderer.py',
            '--output-dir', './test_simple_output',
            '--dpi', '72',
            '--image-size', '6', '4'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✓ Simple renderer test passed")
            return True
        else:
            print(f"✗ Simple renderer test failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"✗ Simple renderer test error: {e}")
        return False

def test_advanced_renderer(test_files):
    """Test the advanced renderer"""
    print("\nTesting advanced renderer...")
    
    try:
        cmd = [
            sys.executable, '3d_image_generator.py',
            '--output-dir', './test_advanced_output',
            '--image-size', '400', '300'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("✓ Advanced renderer test passed")
            return True
        else:
            print(f"✗ Advanced renderer test failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"✗ Advanced renderer test error: {e}")
        return False

def main():
    """Main test function"""
    print("3D Renderer Test Script")
    print("=" * 30)
    
    # Check dependencies
    basic_ok, advanced_ok = check_dependencies()
    
    # Find sample files
    test_files = find_sample_files()
    if not test_files:
        print("No 3D files found for testing")
        return
    
    print(f"\nFound test files: {test_files}")
    
    # Test simple renderer if basic dependencies are available
    if basic_ok:
        simple_success = test_simple_renderer(test_files)
    else:
        print("Skipping simple renderer test (missing dependencies)")
        simple_success = False
    
    # Test advanced renderer if advanced dependencies are available
    if advanced_ok:
        advanced_success = test_advanced_renderer(test_files)
    else:
        print("Skipping advanced renderer test (missing dependencies)")
        advanced_success = False
    
    # Summary
    print("\n" + "=" * 30)
    print("Test Summary:")
    if simple_success:
        print("✓ Simple renderer: PASSED")
    else:
        print("✗ Simple renderer: FAILED")
    
    if advanced_success:
        print("✓ Advanced renderer: PASSED")
    else:
        print("✗ Advanced renderer: FAILED")
    
    if simple_success or advanced_success:
        print("\n🎉 At least one renderer is working!")
        print("Check the test output directories for generated images.")
    else:
        print("\n❌ Both renderers failed. Please check dependencies.")

if __name__ == "__main__":
    main()
