#!/usr/bin/env python3
"""
Setup script for 3D Image Generator

This script helps install the required dependencies for the 3D image generator.
"""

import subprocess
import sys
import os

def install_package(package):
    """Install a package using pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ Successfully installed {package}")
        return True
    except subprocess.CalledProcessError:
        print(f"✗ Failed to install {package}")
        return False

def main():
    """Main setup function"""
    print("Setting up 3D Image Generator...")
    print("=" * 50)
    
    # Basic packages that should work on most systems
    basic_packages = [
        "numpy",
        "matplotlib",
        "Pillow",
    ]
    
    # Advanced packages for full 3D rendering
    advanced_packages = [
        "trimesh",
        "pyrender",
        "pyglet",
        "PyOpenGL",
        "imageio",
        "networkx",
    ]
    
    print("Installing basic packages...")
    basic_success = True
    for package in basic_packages:
        if not install_package(package):
            basic_success = False
    
    if basic_success:
        print("\n✓ Basic packages installed successfully!")
        print("You can use 'simple_3d_renderer.py' for OBJ and PLY files.")
    else:
        print("\n✗ Some basic packages failed to install.")
        return
    
    print("\nInstalling advanced packages for full 3D support...")
    advanced_success = True
    for package in advanced_packages:
        if not install_package(package):
            advanced_success = False
    
    if advanced_success:
        print("\n✓ All packages installed successfully!")
        print("You can use both '3d_image_generator.py' and 'simple_3d_renderer.py'.")
    else:
        print("\n⚠ Some advanced packages failed to install.")
        print("You can still use 'simple_3d_renderer.py' for basic functionality.")
    
    print("\n" + "=" * 50)
    print("Setup complete!")
    print("\nUsage examples:")
    print("  python simple_3d_renderer.py --input-dir . --output-dir ./views")
    print("  python 3d_image_generator.py --input-dir . --output-dir ./views")

if __name__ == "__main__":
    main()
