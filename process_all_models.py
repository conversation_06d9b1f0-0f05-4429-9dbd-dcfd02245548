#!/usr/bin/env python3
"""
Process All 3D Models Script

This script processes all 3D files in your directory, using the appropriate
renderer for each file type.
"""

import os
import subprocess
import sys
import glob
from pathlib import Path

def find_files_by_extension(directory, extensions):
    """Find files by extensions"""
    files = []
    for ext in extensions:
        pattern = os.path.join(directory, f"*.{ext}")
        files.extend(glob.glob(pattern))
        # Also search for uppercase
        pattern = os.path.join(directory, f"*.{ext.upper()}")
        files.extend(glob.glob(pattern))
    return sorted(list(set(files)))

def main():
    """Main processing function"""
    print("🚀 Processing All 3D Models")
    print("=" * 50)
    
    # Find different file types
    obj_files = find_files_by_extension(".", ["obj"])
    fbx_files = find_files_by_extension(".", ["fbx"])
    
    print(f"Found {len(obj_files)} OBJ files")
    print(f"Found {len(fbx_files)} FBX files")
    
    # Process OBJ files with simple renderer
    if obj_files:
        print("\n📁 Processing OBJ files with simple renderer...")
        try:
            cmd = [
                sys.executable, "simple_3d_renderer.py",
                "--output-dir", "./rendered_views",
                "--dpi", "100",
                "--image-size", "8", "6"
            ]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ OBJ files processed successfully!")
            else:
                print(f"❌ Error processing OBJ files: {result.stderr}")
                
        except Exception as e:
            print(f"❌ Error running simple renderer: {e}")
    
    # Handle FBX files
    if fbx_files:
        print("\n📁 FBX files found but not directly supported")
        print("💡 Recommendations for FBX files:")
        print("   1. Convert FBX to OBJ using Blender:")
        print("      - Open Blender")
        print("      - Import FBX file")
        print("      - Export as OBJ")
        print("   2. Use online converters like:")
        print("      - https://www.aspose.app/3d/conversion/fbx-to-obj")
        print("      - https://products.aspose.app/3d/conversion/fbx-to-obj")
        print("   3. Use FBX SDK or other tools")
        
        # Create a batch conversion script suggestion
        print("\n📝 Creating FBX conversion helper script...")
        
        conversion_script = """#!/usr/bin/env python3
# FBX to OBJ Conversion Helper
# This script shows how to convert FBX files using Blender in batch

import subprocess
import os

def convert_fbx_to_obj_with_blender(fbx_file, output_dir):
    \"\"\"Convert FBX to OBJ using Blender command line\"\"\"
    obj_file = os.path.join(output_dir, os.path.splitext(os.path.basename(fbx_file))[0] + '.obj')
    
    # Blender command to convert FBX to OBJ
    cmd = [
        'blender',  # Make sure Blender is in PATH
        '--background',
        '--python-expr',
        f'''
import bpy
bpy.ops.wm.read_factory_settings(use_empty=True)
bpy.ops.import_scene.fbx(filepath="{fbx_file}")
bpy.ops.export_scene.obj(filepath="{obj_file}")
'''
    ]
    
    try:
        subprocess.run(cmd, check=True)
        print(f"Converted: {fbx_file} -> {obj_file}")
        return True
    except subprocess.CalledProcessError:
        print(f"Failed to convert: {fbx_file}")
        return False

# Example usage:
# convert_fbx_to_obj_with_blender("model.fbx", "./converted/")
"""
        
        with open("fbx_converter_helper.py", "w") as f:
            f.write(conversion_script)
        
        print("✅ Created 'fbx_converter_helper.py' for reference")
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 SUMMARY:")
    if obj_files:
        print(f"✅ {len(obj_files)} OBJ files processed")
        print("   Check './rendered_views/' folder for images")
    if fbx_files:
        print(f"⚠️  {len(fbx_files)} FBX files need conversion")
        print("   Use the suggestions above to convert them")
    
    print("\n🎯 NEXT STEPS:")
    print("1. Check the './rendered_views/' folder for generated images")
    print("2. Each model has its own folder with front.png, side.png, back.png")
    if fbx_files:
        print("3. Convert FBX files to OBJ and run the script again")

if __name__ == "__main__":
    main()
