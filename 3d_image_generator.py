#!/usr/bin/env python3
"""
3D Model Image Generator

This script searches for 3D files (.obj, .fbx, .ply, .stl, .dae, .gltf, .glb) 
and generates front, side, and back view images for each model.
"""

import os
import glob
import numpy as np
import trimesh
import pyrender
from PIL import Image
import argparse
import logging
from pathlib import Path
import warnings

# Suppress warnings
warnings.filterwarnings("ignore")

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Model3DRenderer:
    """Class to handle 3D model loading and rendering"""
    
    def __init__(self, image_size=(800, 600)):
        """
        Initialize the renderer
        
        Args:
            image_size (tuple): Output image size (width, height)
        """
        self.image_size = image_size
        self.supported_formats = ['.obj', '.fbx', '.ply', '.stl', '.dae', '.gltf', '.glb']
        
    def load_model(self, file_path):
        """
        Load a 3D model from file
        
        Args:
            file_path (str): Path to the 3D model file
            
        Returns:
            trimesh.Scene or trimesh.Trimesh: Loaded 3D model
        """
        try:
            # Load the model using trimesh
            model = trimesh.load(file_path)
            
            # If it's a scene, try to get the main geometry
            if isinstance(model, trimesh.Scene):
                # Get all geometries and combine them
                geometries = []
                for geometry in model.geometry.values():
                    if hasattr(geometry, 'vertices') and len(geometry.vertices) > 0:
                        geometries.append(geometry)
                
                if geometries:
                    # Combine all geometries
                    if len(geometries) == 1:
                        model = geometries[0]
                    else:
                        model = trimesh.util.concatenate(geometries)
                else:
                    logger.warning(f"No valid geometry found in {file_path}")
                    return None
            
            # Ensure the model has vertices
            if not hasattr(model, 'vertices') or len(model.vertices) == 0:
                logger.warning(f"Model {file_path} has no vertices")
                return None
                
            return model
            
        except Exception as e:
            logger.error(f"Error loading model {file_path}: {str(e)}")
            return None
    
    def setup_camera_and_light(self, scene, camera_position, target_position):
        """
        Setup camera and lighting for the scene
        
        Args:
            scene (pyrender.Scene): The rendering scene
            camera_position (np.array): Camera position
            target_position (np.array): Camera target position
        """
        # Create camera
        camera = pyrender.PerspectiveCamera(yfov=np.pi / 3.0, aspectRatio=self.image_size[0]/self.image_size[1])
        
        # Calculate camera transform
        camera_direction = target_position - camera_position
        camera_direction = camera_direction / np.linalg.norm(camera_direction)
        
        # Create camera transform matrix
        up = np.array([0, 1, 0])
        right = np.cross(camera_direction, up)
        right = right / np.linalg.norm(right)
        up = np.cross(right, camera_direction)
        
        camera_transform = np.eye(4)
        camera_transform[:3, 0] = right
        camera_transform[:3, 1] = up
        camera_transform[:3, 2] = -camera_direction
        camera_transform[:3, 3] = camera_position
        
        scene.add(camera, pose=camera_transform)
        
        # Add lights
        light = pyrender.DirectionalLight(color=np.ones(3), intensity=3.0)
        scene.add(light, pose=camera_transform)
        
        # Add ambient light
        ambient_light = pyrender.DirectionalLight(color=np.ones(3), intensity=1.0)
        light_pose = np.eye(4)
        light_pose[:3, 3] = camera_position + np.array([1, 1, 1])
        scene.add(ambient_light, pose=light_pose)
    
    def render_view(self, model, camera_position, target_position):
        """
        Render a single view of the model
        
        Args:
            model: The 3D model (trimesh object)
            camera_position (np.array): Camera position
            target_position (np.array): Camera target position
            
        Returns:
            PIL.Image: Rendered image
        """
        try:
            # Create pyrender scene
            scene = pyrender.Scene(ambient_light=np.array([0.3, 0.3, 0.3]))
            
            # Convert trimesh to pyrender mesh
            if hasattr(model, 'visual') and hasattr(model.visual, 'material'):
                # Use existing material if available
                mesh = pyrender.Mesh.from_trimesh(model)
            else:
                # Create a default material
                material = pyrender.MetallicRoughnessMaterial(
                    baseColorFactor=[0.8, 0.8, 0.8, 1.0],
                    metallicFactor=0.1,
                    roughnessFactor=0.8
                )
                mesh = pyrender.Mesh.from_trimesh(model, material=material)
            
            scene.add(mesh)
            
            # Setup camera and lighting
            self.setup_camera_and_light(scene, camera_position, target_position)
            
            # Render
            renderer = pyrender.OffscreenRenderer(self.image_size[0], self.image_size[1])
            color, depth = renderer.render(scene)
            renderer.delete()
            
            # Convert to PIL Image
            image = Image.fromarray(color)
            return image
            
        except Exception as e:
            logger.error(f"Error rendering view: {str(e)}")
            return None
    
    def generate_views(self, model):
        """
        Generate front, side, and back views of the model
        
        Args:
            model: The 3D model
            
        Returns:
            dict: Dictionary containing the rendered views
        """
        # Get model bounds
        bounds = model.bounds
        center = model.centroid
        size = np.max(bounds[1] - bounds[0])
        
        # Calculate camera distance (adjust this multiplier as needed)
        camera_distance = size * 2.5
        
        views = {}
        
        # Front view (looking along negative Z axis)
        front_camera_pos = center + np.array([0, 0, camera_distance])
        views['front'] = self.render_view(model, front_camera_pos, center)
        
        # Side view (looking along negative X axis)
        side_camera_pos = center + np.array([camera_distance, 0, 0])
        views['side'] = self.render_view(model, side_camera_pos, center)
        
        # Back view (looking along positive Z axis)
        back_camera_pos = center + np.array([0, 0, -camera_distance])
        views['back'] = self.render_view(model, back_camera_pos, center)
        
        return views

def find_3d_files(directory):
    """
    Find all 3D files in the given directory
    
    Args:
        directory (str): Directory to search
        
    Returns:
        list: List of 3D file paths
    """
    supported_extensions = ['*.obj', '*.fbx', '*.ply', '*.stl', '*.dae', '*.gltf', '*.glb']
    files = []
    
    for extension in supported_extensions:
        pattern = os.path.join(directory, f"**/{extension}")
        files.extend(glob.glob(pattern, recursive=True))
        # Also search for uppercase extensions
        pattern = os.path.join(directory, f"**/{extension.upper()}")
        files.extend(glob.glob(pattern, recursive=True))
    
    return sorted(list(set(files)))  # Remove duplicates and sort

def create_output_folder(file_path, output_base_dir):
    """
    Create output folder for a 3D file
    
    Args:
        file_path (str): Path to the 3D file
        output_base_dir (str): Base output directory
        
    Returns:
        str: Path to the created output folder
    """
    file_name = Path(file_path).stem
    output_folder = os.path.join(output_base_dir, file_name)
    os.makedirs(output_folder, exist_ok=True)
    return output_folder

def process_3d_file(file_path, output_base_dir, renderer):
    """
    Process a single 3D file and generate view images
    
    Args:
        file_path (str): Path to the 3D file
        output_base_dir (str): Base output directory
        renderer (Model3DRenderer): The renderer instance
    """
    logger.info(f"Processing: {file_path}")
    
    # Load the model
    model = renderer.load_model(file_path)
    if model is None:
        logger.error(f"Failed to load model: {file_path}")
        return
    
    # Create output folder
    output_folder = create_output_folder(file_path, output_base_dir)
    
    # Generate views
    views = renderer.generate_views(model)
    
    # Save images
    for view_name, image in views.items():
        if image is not None:
            output_path = os.path.join(output_folder, f"{view_name}.png")
            image.save(output_path)
            logger.info(f"Saved {view_name} view: {output_path}")
        else:
            logger.warning(f"Failed to generate {view_name} view for {file_path}")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Generate front, side, and back view images from 3D models")
    parser.add_argument("--input-dir", "-i", default=".", help="Input directory containing 3D files (default: current directory)")
    parser.add_argument("--output-dir", "-o", default="./rendered_views", help="Output directory for generated images")
    parser.add_argument("--image-size", "-s", nargs=2, type=int, default=[800, 600], help="Output image size (width height)")
    parser.add_argument("--recursive", "-r", action="store_true", help="Search recursively in subdirectories")
    
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Find 3D files
    if args.recursive:
        files = find_3d_files(args.input_dir)
    else:
        # Search only in the specified directory
        supported_extensions = ['*.obj', '*.fbx', '*.ply', '*.stl', '*.dae', '*.gltf', '*.glb']
        files = []
        for extension in supported_extensions:
            pattern = os.path.join(args.input_dir, extension)
            files.extend(glob.glob(pattern))
            # Also search for uppercase extensions
            pattern = os.path.join(args.input_dir, extension.upper())
            files.extend(glob.glob(pattern))
        files = sorted(list(set(files)))
    
    if not files:
        logger.warning(f"No 3D files found in {args.input_dir}")
        return
    
    logger.info(f"Found {len(files)} 3D files")
    
    # Initialize renderer
    renderer = Model3DRenderer(image_size=tuple(args.image_size))
    
    # Process each file
    for file_path in files:
        try:
            process_3d_file(file_path, args.output_dir, renderer)
        except Exception as e:
            logger.error(f"Error processing {file_path}: {str(e)}")
            continue
    
    logger.info("Processing complete!")

if __name__ == "__main__":
    main()
