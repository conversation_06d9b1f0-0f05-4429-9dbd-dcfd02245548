#!/usr/bin/env python3
"""
Simple 3D Model Image Generator

A simplified version that focuses on basic 3D file formats and uses matplotlib for rendering.
This version is more compatible with various systems and doesn't require OpenGL.
"""

import os
import glob
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from pathlib import Path
import logging
import argparse

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Simple3DRenderer:
    """Simple 3D renderer using matplotlib"""
    
    def __init__(self, image_size=(10, 8), dpi=100):
        """
        Initialize the renderer
        
        Args:
            image_size (tuple): Figure size in inches
            dpi (int): DPI for output images
        """
        self.image_size = image_size
        self.dpi = dpi
        
    def load_obj_file(self, file_path):
        """
        Load an OBJ file and extract vertices and faces
        
        Args:
            file_path (str): Path to the OBJ file
            
        Returns:
            tuple: (vertices, faces) or (None, None) if failed
        """
        try:
            vertices = []
            faces = []
            
            with open(file_path, 'r') as file:
                for line in file:
                    line = line.strip()
                    if line.startswith('v '):
                        # Vertex
                        parts = line.split()
                        if len(parts) >= 4:
                            x, y, z = float(parts[1]), float(parts[2]), float(parts[3])
                            vertices.append([x, y, z])
                    elif line.startswith('f '):
                        # Face
                        parts = line.split()
                        face = []
                        for part in parts[1:]:
                            # Handle different face formats (v, v/vt, v/vt/vn)
                            vertex_index = int(part.split('/')[0]) - 1  # OBJ indices start at 1
                            face.append(vertex_index)
                        if len(face) >= 3:
                            faces.append(face)
            
            if vertices:
                return np.array(vertices), faces
            else:
                return None, None
                
        except Exception as e:
            logger.error(f"Error loading OBJ file {file_path}: {str(e)}")
            return None, None
    
    def load_ply_file(self, file_path):
        """
        Load a PLY file and extract vertices
        
        Args:
            file_path (str): Path to the PLY file
            
        Returns:
            tuple: (vertices, None) or (None, None) if failed
        """
        try:
            vertices = []
            vertex_count = 0
            header_ended = False
            
            with open(file_path, 'r') as file:
                for line in file:
                    line = line.strip()
                    
                    if not header_ended:
                        if line.startswith('element vertex'):
                            vertex_count = int(line.split()[-1])
                        elif line == 'end_header':
                            header_ended = True
                        continue
                    
                    # Read vertex data
                    if len(vertices) < vertex_count:
                        parts = line.split()
                        if len(parts) >= 3:
                            x, y, z = float(parts[0]), float(parts[1]), float(parts[2])
                            vertices.append([x, y, z])
            
            if vertices:
                return np.array(vertices), None
            else:
                return None, None
                
        except Exception as e:
            logger.error(f"Error loading PLY file {file_path}: {str(e)}")
            return None, None
    
    def load_model(self, file_path):
        """
        Load a 3D model from supported formats
        
        Args:
            file_path (str): Path to the 3D model file
            
        Returns:
            tuple: (vertices, faces) or (None, None) if failed
        """
        file_ext = Path(file_path).suffix.lower()
        
        if file_ext == '.obj':
            return self.load_obj_file(file_path)
        elif file_ext == '.ply':
            return self.load_ply_file(file_path)
        else:
            logger.warning(f"Unsupported file format: {file_ext}")
            return None, None
    
    def render_view(self, vertices, faces, view_angle, title):
        """
        Render a view of the 3D model
        
        Args:
            vertices (np.array): Model vertices
            faces (list): Model faces (can be None)
            view_angle (tuple): (elevation, azimuth) for the view
            title (str): Title for the plot
            
        Returns:
            matplotlib.figure.Figure: The rendered figure
        """
        fig = plt.figure(figsize=self.image_size, dpi=self.dpi)
        ax = fig.add_subplot(111, projection='3d')
        
        # Plot vertices as scatter plot
        ax.scatter(vertices[:, 0], vertices[:, 1], vertices[:, 2], 
                  c='blue', s=1, alpha=0.6)
        
        # If faces are available, plot wireframe
        if faces:
            for face in faces[:min(len(faces), 1000)]:  # Limit faces for performance
                if len(face) >= 3:
                    # Create triangles from face
                    for i in range(len(face) - 2):
                        triangle = [face[0], face[i+1], face[i+2]]
                        triangle_vertices = vertices[triangle]
                        
                        # Plot triangle edges
                        for j in range(3):
                            start = triangle_vertices[j]
                            end = triangle_vertices[(j+1) % 3]
                            ax.plot([start[0], end[0]], 
                                   [start[1], end[1]], 
                                   [start[2], end[2]], 
                                   'b-', alpha=0.3, linewidth=0.5)
        
        # Set equal aspect ratio
        max_range = np.array([vertices[:, 0].max() - vertices[:, 0].min(),
                             vertices[:, 1].max() - vertices[:, 1].min(),
                             vertices[:, 2].max() - vertices[:, 2].min()]).max() / 2.0
        
        mid_x = (vertices[:, 0].max() + vertices[:, 0].min()) * 0.5
        mid_y = (vertices[:, 1].max() + vertices[:, 1].min()) * 0.5
        mid_z = (vertices[:, 2].max() + vertices[:, 2].min()) * 0.5
        
        ax.set_xlim(mid_x - max_range, mid_x + max_range)
        ax.set_ylim(mid_y - max_range, mid_y + max_range)
        ax.set_zlim(mid_z - max_range, mid_z + max_range)
        
        # Set view angle
        ax.view_init(elev=view_angle[0], azim=view_angle[1])
        
        # Remove axes for cleaner look
        ax.set_xlabel('')
        ax.set_ylabel('')
        ax.set_zlabel('')
        ax.set_title(title)
        
        # Remove tick labels
        ax.set_xticklabels([])
        ax.set_yticklabels([])
        ax.set_zticklabels([])
        
        plt.tight_layout()
        return fig
    
    def generate_views(self, vertices, faces):
        """
        Generate front, side, and back views
        
        Args:
            vertices (np.array): Model vertices
            faces (list): Model faces
            
        Returns:
            dict: Dictionary of rendered figures
        """
        views = {}
        
        # Define view angles (elevation, azimuth)
        view_angles = {
            'front': (0, 0),      # Front view
            'side': (0, 90),      # Side view
            'back': (0, 180),     # Back view
        }
        
        for view_name, angle in view_angles.items():
            views[view_name] = self.render_view(vertices, faces, angle, f"{view_name.title()} View")
        
        return views

def find_3d_files(directory, recursive=False):
    """
    Find supported 3D files in the directory
    
    Args:
        directory (str): Directory to search
        recursive (bool): Whether to search recursively
        
    Returns:
        list: List of 3D file paths
    """
    supported_extensions = ['*.obj', '*.ply']
    files = []
    
    for extension in supported_extensions:
        if recursive:
            pattern = os.path.join(directory, f"**/{extension}")
            files.extend(glob.glob(pattern, recursive=True))
            # Also search for uppercase extensions
            pattern = os.path.join(directory, f"**/{extension.upper()}")
            files.extend(glob.glob(pattern, recursive=True))
        else:
            pattern = os.path.join(directory, extension)
            files.extend(glob.glob(pattern))
            # Also search for uppercase extensions
            pattern = os.path.join(directory, extension.upper())
            files.extend(glob.glob(pattern))
    
    return sorted(list(set(files)))

def create_output_folder(file_path, output_base_dir):
    """
    Create output folder for a 3D file
    
    Args:
        file_path (str): Path to the 3D file
        output_base_dir (str): Base output directory
        
    Returns:
        str: Path to the created output folder
    """
    file_name = Path(file_path).stem
    output_folder = os.path.join(output_base_dir, file_name)
    os.makedirs(output_folder, exist_ok=True)
    return output_folder

def process_3d_file(file_path, output_base_dir, renderer):
    """
    Process a single 3D file and generate view images
    
    Args:
        file_path (str): Path to the 3D file
        output_base_dir (str): Base output directory
        renderer (Simple3DRenderer): The renderer instance
    """
    logger.info(f"Processing: {file_path}")
    
    # Load the model
    vertices, faces = renderer.load_model(file_path)
    if vertices is None:
        logger.error(f"Failed to load model: {file_path}")
        return
    
    logger.info(f"Loaded model with {len(vertices)} vertices")
    
    # Create output folder
    output_folder = create_output_folder(file_path, output_base_dir)
    
    # Generate views
    views = renderer.generate_views(vertices, faces)
    
    # Save images
    for view_name, figure in views.items():
        output_path = os.path.join(output_folder, f"{view_name}.png")
        figure.savefig(output_path, bbox_inches='tight', dpi=renderer.dpi)
        plt.close(figure)  # Close figure to free memory
        logger.info(f"Saved {view_name} view: {output_path}")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Generate front, side, and back view images from 3D models (OBJ/PLY)")
    parser.add_argument("--input-dir", "-i", default=".", help="Input directory containing 3D files (default: current directory)")
    parser.add_argument("--output-dir", "-o", default="./rendered_views", help="Output directory for generated images")
    parser.add_argument("--recursive", "-r", action="store_true", help="Search recursively in subdirectories")
    parser.add_argument("--image-size", "-s", nargs=2, type=float, default=[10, 8], help="Output image size in inches (width height)")
    parser.add_argument("--dpi", "-d", type=int, default=100, help="DPI for output images")
    
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Find 3D files
    files = find_3d_files(args.input_dir, args.recursive)
    
    if not files:
        logger.warning(f"No supported 3D files (OBJ/PLY) found in {args.input_dir}")
        return
    
    logger.info(f"Found {len(files)} 3D files")
    
    # Initialize renderer
    renderer = Simple3DRenderer(image_size=tuple(args.image_size), dpi=args.dpi)
    
    # Process each file
    for file_path in files:
        try:
            process_3d_file(file_path, args.output_dir, renderer)
        except Exception as e:
            logger.error(f"Error processing {file_path}: {str(e)}")
            continue
    
    logger.info("Processing complete!")

if __name__ == "__main__":
    main()
